"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkout/page",{

/***/ "(app-pages-browser)/./src/app/checkout/page.tsx":
/*!***********************************!*\
  !*** ./src/app/checkout/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckoutPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction CheckoutPageContent() {\n    console.log('🔥 CheckoutPageContent function called');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActualCheckoutContent, {}, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\n_c = CheckoutPageContent;\nfunction ActualCheckoutContent() {\n    _s();\n    // Immediate debug log\n    console.log('🔥 ActualCheckoutContent function called');\n    // React hooks must be at the top level\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.createSupabaseBrowserClient)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    console.log('🔥 ActualCheckoutContent - all hooks initialized');\n    const selectedPlan = searchParams.get('plan') || 'professional';\n    const userId = searchParams.get('user_id');\n    const email = searchParams.get('email');\n    const isSignup = searchParams.get('signup') === 'true';\n    // Debug the URL params immediately\n    console.log('🔍 ActualCheckoutContent URL params parsed:', {\n        selectedPlan,\n        userId,\n        email,\n        isSignup\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ActualCheckoutContent.useEffect\": ()=>{\n            console.log('🚀 ActualCheckoutContent first useEffect - component mounting...');\n            fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'COMPONENT_MOUNT',\n                    message: 'ActualCheckoutContent component is mounting'\n                })\n            }).catch({\n                \"ActualCheckoutContent.useEffect\": ()=>{}\n            }[\"ActualCheckoutContent.useEffect\"]);\n            console.log('🚀 ActualCheckoutContent - setting mounted to true');\n            setMounted(true);\n        }\n    }[\"ActualCheckoutContent.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ActualCheckoutContent.useEffect\": ()=>{\n            console.log('🚀 ActualCheckoutContent second useEffect - mounted:', mounted);\n            if (!mounted) {\n                console.log('🚀 ActualCheckoutContent - not mounted yet, returning');\n                return;\n            }\n            console.log('=== CHECKOUT PAGE MOUNTED ===');\n            console.log('URL params:', {\n                selectedPlan,\n                userId,\n                email,\n                isSignup\n            });\n            console.log('Current URL:', window.location.href);\n            // Check localStorage for pending signup\n            const pendingSignup = localStorage.getItem('pending_signup');\n            console.log('localStorage pending_signup:', pendingSignup ? 'FOUND' : 'NOT FOUND');\n            if (pendingSignup) {\n                console.log('Pending signup data:', JSON.parse(pendingSignup));\n            }\n            // Add debug function to window for manual testing\n            window.debugCheckout = ({\n                \"ActualCheckoutContent.useEffect\": ()=>{\n                    console.log('=== DEBUG CHECKOUT ===');\n                    console.log('localStorage pending_signup:', localStorage.getItem('pending_signup'));\n                    console.log('URL params:', {\n                        selectedPlan,\n                        userId,\n                        email,\n                        isSignup\n                    });\n                }\n            })[\"ActualCheckoutContent.useEffect\"];\n            // Listen for auth state changes to handle recent sign-ins\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"ActualCheckoutContent.useEffect\": async (event, session)=>{\n                    console.log('Auth state change detected:', event, !!session);\n                    if (event === 'SIGNED_IN' && session) {\n                        console.log('User just signed in, proceeding with checkout...');\n                        // Small delay to ensure session is fully established\n                        setTimeout({\n                            \"ActualCheckoutContent.useEffect\": ()=>{\n                                initializeCheckout();\n                            }\n                        }[\"ActualCheckoutContent.useEffect\"], 100);\n                    }\n                }\n            }[\"ActualCheckoutContent.useEffect\"]);\n            initializeCheckout();\n            // Cleanup subscription\n            return ({\n                \"ActualCheckoutContent.useEffect\": ()=>subscription.unsubscribe()\n            })[\"ActualCheckoutContent.useEffect\"];\n        }\n    }[\"ActualCheckoutContent.useEffect\"], [\n        mounted\n    ]);\n    const initializeCheckout = async ()=>{\n        try {\n            var _session_user, _session_user1;\n            // Send debug info to server for terminal logging\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'CHECKOUT_INITIALIZATION',\n                    urlParams: {\n                        selectedPlan,\n                        userId,\n                        email,\n                        isSignup\n                    },\n                    currentUrl: window.location.href\n                })\n            }).catch(()=>{}); // Don't fail if debug endpoint fails\n            console.log('=== CHECKOUT INITIALIZATION ===');\n            console.log('URL params:', {\n                selectedPlan,\n                userId,\n                email,\n                isSignup\n            });\n            // Try to get session with retry logic for recent sign-ins\n            let session = null;\n            let sessionError = null;\n            let retryCount = 0;\n            const maxRetries = 3;\n            while(!session && retryCount < maxRetries){\n                const result = await supabase.auth.getSession();\n                session = result.data.session;\n                sessionError = result.error;\n                if (!session && retryCount < maxRetries - 1) {\n                    console.log(\"Session not found, retrying in 1 second... (attempt \".concat(retryCount + 1, \"/\").concat(maxRetries, \")\"));\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                }\n                retryCount++;\n            }\n            console.log('Session check:', {\n                hasSession: !!session,\n                error: sessionError,\n                retryCount\n            });\n            // Debug session details\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'SESSION_CHECK',\n                    hasSession: !!session,\n                    sessionError: sessionError === null || sessionError === void 0 ? void 0 : sessionError.message,\n                    userId: session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id,\n                    userEmail: session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.email,\n                    retryCount: retryCount,\n                    maxRetries: maxRetries\n                })\n            }).catch(()=>{});\n            if (sessionError || !session) {\n                console.log('No valid session found after', retryCount, 'attempts');\n                console.log('Session error:', sessionError);\n                console.log('Session data:', session);\n                await fetch('/api/debug/checkout', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'SESSION_FAILED',\n                        error: (sessionError === null || sessionError === void 0 ? void 0 : sessionError.message) || 'No session found',\n                        userId: userId,\n                        email: email,\n                        hasUserId: !!userId,\n                        hasEmail: !!email,\n                        retryCount: retryCount,\n                        redirecting: true\n                    })\n                }).catch(()=>{});\n                // If we have a userId from URL params, this means user was created but not signed in\n                // We need to prompt them to sign in with their credentials\n                if (userId) {\n                    setError('Please sign in with your account credentials to complete checkout.');\n                    const signinUrl = \"/auth/signin?plan=\".concat(selectedPlan, \"&checkout_user_id=\").concat(userId).concat(email ? \"&email=\".concat(encodeURIComponent(email)) : '');\n                    setTimeout(()=>router.push(signinUrl), 3000);\n                } else {\n                    setError('Authentication required. Please sign up first.');\n                    setTimeout(()=>router.push(\"/auth/signup?plan=\".concat(selectedPlan)), 3000);\n                }\n                return;\n            }\n            setUser(session.user);\n            console.log('Set user state:', session.user);\n            // Check if user has payment_pending status (new signup)\n            const userMetadata = session.user.user_metadata;\n            const paymentStatus = userMetadata === null || userMetadata === void 0 ? void 0 : userMetadata.payment_status;\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'USER_SESSION_FOUND',\n                    userId: session.user.id,\n                    email: session.user.email,\n                    paymentStatus,\n                    userMetadata\n                })\n            }).catch(()=>{});\n            console.log('Processing checkout for user:', session.user.id);\n            console.log('Payment status:', paymentStatus);\n            // Debug before calling createCheckoutSession\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'CALLING_CREATE_CHECKOUT_SESSION',\n                    userId: session.user.id,\n                    selectedPlan,\n                    aboutToCall: true\n                })\n            }).catch(()=>{});\n            console.log('About to call createCheckoutSession...');\n            try {\n                // Create checkout session for authenticated user\n                // Pass the session user directly instead of relying on state\n                await createCheckoutSession(session.user.id, session.user);\n                console.log('createCheckoutSession call completed successfully');\n            } catch (checkoutError) {\n                console.error('Error in createCheckoutSession:', checkoutError);\n                await fetch('/api/debug/checkout', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'CREATE_CHECKOUT_SESSION_ERROR',\n                        error: checkoutError.message,\n                        stack: checkoutError.stack\n                    })\n                }).catch(()=>{});\n                throw checkoutError; // Re-throw to be caught by outer catch\n            }\n        } catch (error) {\n            console.error('Checkout initialization error:', error);\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'ERROR',\n                    error: error.message\n                })\n            }).catch(()=>{});\n            setError('Failed to initialize checkout. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const createCheckoutSessionForSignup = async (userData)=>{\n        try {\n            const requestData = {\n                priceId: getPriceId(selectedPlan),\n                tier: selectedPlan,\n                userEmail: userData.email,\n                signup: true,\n                pendingUserData: userData\n            };\n            console.log('Creating checkout session for signup:', requestData);\n            console.log('Environment check:', {\n                hasStarterPrice: !!\"price_1RaA5xC97XFBBUvdt12n1i0T\",\n                hasProfessionalPrice: !!\"price_1RaABVC97XFBBUvdkZZc1oQB\",\n                hasEnterprisePrice: !!\"price_1RaADDC97XFBBUvd7j6OPJj7\"\n            });\n            const response = await fetch('/api/stripe/create-checkout-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestData)\n            });\n            const data = await response.json();\n            console.log('Checkout session response:', {\n                ok: response.ok,\n                status: response.status,\n                data\n            });\n            if (!response.ok) {\n                console.error('Checkout session creation failed:', data);\n                throw new Error(data.error || 'Failed to create checkout session');\n            }\n            // Redirect to Stripe Checkout\n            if (data.url) {\n                window.location.href = data.url;\n            } else {\n                throw new Error('No checkout URL received');\n            }\n        } catch (error) {\n            console.error('Signup checkout session error:', error);\n            setError(error instanceof Error ? error.message : 'Failed to start checkout');\n        }\n    };\n    const createCheckoutSession = async (userId, sessionUser)=>{\n        console.log('🚀 createCheckoutSession function called with userId:', userId);\n        try {\n            var _currentUser_user_metadata, _currentUser_user_metadata1;\n            // Use passed sessionUser or fallback to state user\n            const currentUser = sessionUser || user;\n            // Get email from multiple sources\n            const userEmail = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.email) || (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser_user_metadata = currentUser.user_metadata) === null || _currentUser_user_metadata === void 0 ? void 0 : _currentUser_user_metadata.email) || email;\n            console.log('Email extraction debug:', {\n                currentUserEmail: currentUser === null || currentUser === void 0 ? void 0 : currentUser.email,\n                currentUserMetadataEmail: currentUser === null || currentUser === void 0 ? void 0 : (_currentUser_user_metadata1 = currentUser.user_metadata) === null || _currentUser_user_metadata1 === void 0 ? void 0 : _currentUser_user_metadata1.email,\n                urlEmail: email,\n                finalEmail: userEmail,\n                currentUserObject: currentUser\n            });\n            if (!userEmail) {\n                console.error('No email found in any source:', {\n                    currentUser,\n                    stateUser: user,\n                    urlEmail: email\n                });\n                throw new Error('User email not found');\n            }\n            console.log('Creating checkout session with:', {\n                priceId: getPriceId(selectedPlan),\n                tier: selectedPlan,\n                userId: userId,\n                userEmail: userEmail\n            });\n            const response = await fetch('/api/stripe/create-checkout-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    priceId: getPriceId(selectedPlan),\n                    tier: selectedPlan,\n                    userId: userId,\n                    userEmail: userEmail,\n                    signup: false\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to create checkout session');\n            }\n            // Redirect to Stripe Checkout\n            if (data.url) {\n                window.location.href = data.url;\n            } else {\n                throw new Error('No checkout URL received');\n            }\n        } catch (error) {\n            console.error('Checkout session error:', error);\n            setError(error instanceof Error ? error.message : 'Failed to start checkout');\n        }\n    };\n    const getPriceId = (plan)=>{\n        switch(plan.toLowerCase()){\n            case 'starter':\n                return \"price_1RaA5xC97XFBBUvdt12n1i0T\" || 0;\n            case 'professional':\n                return \"price_1RaABVC97XFBBUvdkZZc1oQB\" || 0;\n            case 'enterprise':\n                return \"price_1RaADDC97XFBBUvd7j6OPJj7\" || 0;\n            default:\n                return \"price_1RaABVC97XFBBUvdkZZc1oQB\" || 0;\n        }\n    };\n    const getPlanPrice = (plan)=>{\n        switch(plan.toLowerCase()){\n            case 'starter':\n                return '$29';\n            case 'professional':\n                return '$99';\n            case 'enterprise':\n                return '$299';\n            default:\n                return '$99';\n        }\n    };\n    // Show loading until component is mounted (prevents hydration issues)\n    if (!mounted) {\n        console.log('⏳ Component not mounted yet, showing loading...');\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading checkout...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 399,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 398,\n            columnNumber: 7\n        }, this);\n    }\n    console.log('✅ Component mounted, proceeding with render...');\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-8 h-8 text-red-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M6 18L18 6M6 6l12 12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"Checkout Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                        href: \"/auth/signup?plan=\".concat(selectedPlan),\n                        className: \"inline-flex items-center px-6 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#e55a2b] transition-colors\",\n                        children: \"Try Again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 412,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 411,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            src: \"/roukey_logo.png\",\n                            alt: \"RouKey\",\n                            width: 40,\n                            height: 40,\n                            className: \"w-10 h-10 object-contain\",\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 437,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 435,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-xl border border-gray-100 p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 border-4 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: \"Setting up your subscription...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 border border-[#ff6b35]/20 rounded-xl p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-2 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#ff6b35] font-semibold\",\n                                            children: [\n                                                selectedPlan.charAt(0).toUpperCase() + selectedPlan.slice(1),\n                                                \" Plan\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: [\n                                        getPlanPrice(selectedPlan),\n                                        \"/month\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"You'll be redirected to Stripe to complete your payment securely.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"After payment, you'll verify your email and gain access to your dashboard.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 449,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 flex items-center justify-center space-x-2 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 476,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Secured by Stripe\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 475,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 433,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 432,\n        columnNumber: 5\n    }, this);\n}\n_s(ActualCheckoutContent, \"28X6dCLSY33yGsWYE1GDYj1wduQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c1 = ActualCheckoutContent;\nfunction CheckoutPage() {\n    console.log('🚀 CheckoutPage main function called');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CheckoutPageContent, {}, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 488,\n        columnNumber: 10\n    }, this);\n}\n_c2 = CheckoutPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CheckoutPageContent\");\n$RefreshReg$(_c1, \"ActualCheckoutContent\");\n$RefreshReg$(_c2, \"CheckoutPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/checkout/page.tsx\n"));

/***/ })

});