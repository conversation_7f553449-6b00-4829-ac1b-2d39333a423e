"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/signup/page",{

/***/ "(app-pages-browser)/./src/app/auth/signup/page.tsx":
/*!**************************************!*\
  !*** ./src/app/auth/signup/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignUpPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,EyeIcon,EyeSlashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,EyeIcon,EyeSlashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,EyeIcon,EyeSlashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/landing/EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SignUpPageContent() {\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: '',\n        lastName: '',\n        email: '',\n        password: '',\n        confirmPassword: ''\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [agreedToTerms, setAgreedToTerms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_5__.createSupabaseBrowserClient)();\n    // Note: Removed automatic session clearing to avoid conflicts\n    // Get selected plan from URL params\n    const selectedPlan = searchParams.get('plan');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SignUpPageContent.useEffect\": ()=>{\n            // Redirect to pricing if no plan is selected\n            if (!selectedPlan) {\n                router.push('/pricing');\n                return;\n            }\n            // Validate plan\n            if (![\n                'starter',\n                'professional',\n                'enterprise'\n            ].includes(selectedPlan)) {\n                router.push('/pricing');\n                return;\n            }\n            // TEMPORARILY DISABLED: Check if user is already signed in\n            // This is causing redirect loops, so we'll let users manually fill out the form\n            console.log('Signup page loaded, automatic session check disabled to prevent redirect loops');\n        /*\n    const checkUser = async () => {\n      const { data: { session } } = await supabase.auth.getSession();\n      if (session) {\n        console.log('User already signed in, checking subscription status...');\n\n        // Check if user has active subscription\n        const { data: profile } = await supabase\n          .from('user_profiles')\n          .select('subscription_status')\n          .eq('user_id', session.user.id)\n          .single();\n\n        if (profile && profile.subscription_status === 'active') {\n          // User has active subscription, go to dashboard\n          router.push('/dashboard');\n        } else {\n          // User needs to complete payment\n          router.push(`/checkout?plan=${selectedPlan}&user_id=${session.user.id}`);\n        }\n      }\n    };\n    checkUser();\n    */ }\n    }[\"SignUpPageContent.useEffect\"], [\n        router,\n        selectedPlan,\n        supabase\n    ]);\n    const handleChange = (e)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [e.target.name]: e.target.value\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError('');\n        // Validation\n        if (formData.password !== formData.confirmPassword) {\n            setError('Passwords do not match');\n            setIsLoading(false);\n            return;\n        }\n        if (formData.password.length < 8) {\n            setError('Password must be at least 8 characters long');\n            setIsLoading(false);\n            return;\n        }\n        if (!agreedToTerms) {\n            setError('Please agree to the Terms of Service and Privacy Policy');\n            setIsLoading(false);\n            return;\n        }\n        try {\n            // NEW APPROACH: Create user account immediately but mark as payment_pending\n            console.log('Creating user account with payment_pending status...');\n            const { data, error } = await supabase.auth.signUp({\n                email: formData.email,\n                password: formData.password,\n                options: {\n                    data: {\n                        first_name: formData.firstName,\n                        last_name: formData.lastName,\n                        full_name: \"\".concat(formData.firstName, \" \").concat(formData.lastName),\n                        plan: selectedPlan || 'professional',\n                        payment_status: 'pending'\n                    }\n                }\n            });\n            if (error) {\n                if (error.message.includes('already registered')) {\n                    // Check if this is a pending user who can retry\n                    console.log('Email already registered, checking if user has pending payment...');\n                    try {\n                        var _signInData_user_user_metadata;\n                        // Try to sign in to check user status\n                        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({\n                            email: formData.email,\n                            password: formData.password\n                        });\n                        if (signInData.user && ((_signInData_user_user_metadata = signInData.user.user_metadata) === null || _signInData_user_user_metadata === void 0 ? void 0 : _signInData_user_user_metadata.payment_status) === 'pending') {\n                            var _verifySession_user;\n                            console.log('Found pending user, allowing retry of checkout...');\n                            // Debug logging\n                            await fetch('/api/debug/checkout', {\n                                method: 'POST',\n                                headers: {\n                                    'Content-Type': 'application/json'\n                                },\n                                body: JSON.stringify({\n                                    action: 'PENDING_USER_RETRY',\n                                    userId: signInData.user.id,\n                                    email: formData.email,\n                                    selectedPlan,\n                                    message: 'Allowing pending user to retry checkout - user is now signed in',\n                                    sessionExists: !!signInData.session\n                                })\n                            }).catch(()=>{});\n                            // Verify session was established with multiple attempts\n                            let verifySession = null;\n                            let attempts = 0;\n                            const maxAttempts = 3;\n                            while(!verifySession && attempts < maxAttempts){\n                                const { data: { session } } = await supabase.auth.getSession();\n                                verifySession = session;\n                                attempts++;\n                                if (!verifySession && attempts < maxAttempts) {\n                                    console.log(\"Session verification attempt \".concat(attempts, \"/\").concat(maxAttempts, \" failed, retrying...\"));\n                                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                                }\n                            }\n                            console.log('Session verification after sign in:', {\n                                hasSession: !!verifySession,\n                                userId: verifySession === null || verifySession === void 0 ? void 0 : (_verifySession_user = verifySession.user) === null || _verifySession_user === void 0 ? void 0 : _verifySession_user.id,\n                                attempts: attempts\n                            });\n                            if (verifySession && verifySession.user) {\n                                console.log('Session verified, redirecting to checkout...');\n                                // Use router.push instead of window.location.href to maintain session\n                                router.push(\"/checkout?plan=\".concat(selectedPlan, \"&user_id=\").concat(verifySession.user.id, \"&email=\").concat(encodeURIComponent(formData.email)));\n                            } else {\n                                console.error('Session not established after sign in, redirecting to signin page');\n                                setError('Failed to establish session. Please try signing in manually.');\n                                router.push(\"/auth/signin?plan=\".concat(selectedPlan, \"&email=\").concat(encodeURIComponent(formData.email), \"&message=session_failed\"));\n                            }\n                            return;\n                        } else {\n                            console.log('Found active user, redirecting to sign in...');\n                            // Redirect to sign in page for active users\n                            window.location.href = \"/auth/signin?plan=\".concat(selectedPlan, \"&message=account_exists\");\n                            return;\n                        }\n                    } catch (retryError) {\n                        console.error('Error checking pending user:', retryError);\n                        // If we can't determine status, redirect to sign in\n                        window.location.href = \"/auth/signin?plan=\".concat(selectedPlan, \"&message=account_exists\");\n                        return;\n                    }\n                } else {\n                    setError(error.message);\n                }\n                return;\n            }\n            if (!data.user) {\n                setError('Failed to create account. Please try again.');\n                return;\n            }\n            // Debug logging to terminal\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'USER_CREATED_PAYMENT_PENDING',\n                    userId: data.user.id,\n                    email: formData.email,\n                    selectedPlan,\n                    redirectUrl: \"/checkout?plan=\".concat(selectedPlan, \"&user_id=\").concat(data.user.id, \"&email=\").concat(encodeURIComponent(formData.email)),\n                    paymentStatus: 'pending',\n                    message: 'User created successfully, user should be automatically signed in'\n                })\n            }).catch(()=>{});\n            console.log('User created successfully, checking session...');\n            console.log('User ID:', data.user.id);\n            console.log('Plan selected:', selectedPlan);\n            // Check if user is automatically signed in after signup\n            const { data: { session } } = await supabase.auth.getSession();\n            if (session && session.user) {\n                console.log('User is signed in after signup, redirecting to checkout...');\n                // User is signed in, wait a moment for session to propagate then redirect\n                setTimeout(()=>{\n                    window.location.href = \"/checkout?plan=\".concat(selectedPlan, \"&user_id=\").concat(session.user.id, \"&email=\").concat(encodeURIComponent(formData.email));\n                }, 500);\n            } else {\n                console.log('User not automatically signed in, redirecting to signin...');\n                // Fallback: redirect to sign in page\n                window.location.href = \"/auth/signin?plan=\".concat(selectedPlan, \"&checkout_user_id=\").concat(data.user.id, \"&email=\").concat(encodeURIComponent(formData.email), \"&message=account_created\");\n            }\n        } catch (err) {\n            console.error('Sign up error:', err);\n            setError(err.message || 'Failed to process signup. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const passwordRequirements = [\n        {\n            text: 'At least 8 characters',\n            met: formData.password.length >= 8\n        },\n        {\n            text: 'Contains uppercase letter',\n            met: /[A-Z]/.test(formData.password)\n        },\n        {\n            text: 'Contains lowercase letter',\n            met: /[a-z]/.test(formData.password)\n        },\n        {\n            text: 'Contains number',\n            met: /\\d/.test(formData.password)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white relative overflow-hidden flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                gridSize: 50,\n                opacity: 0.064,\n                color: \"#000000\",\n                variant: \"subtle\",\n                animated: true,\n                className: \"fixed inset-0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    style: {\n                        backgroundImage: \"radial-gradient(circle, rgba(255, 107, 53, 0.12) 1px, transparent 1px)\",\n                        backgroundSize: '100px 100px',\n                        mask: \"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)\",\n                        WebkitMask: \"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                lineNumber: 275,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 w-full max-w-6xl mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            className: \"hidden lg:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-3xl p-12 text-white relative overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 opacity-20\",\n                                        style: {\n                                            backgroundImage: \"\\n                    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),\\n                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)\\n                  \",\n                                            backgroundSize: '30px 30px'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mb-6 backdrop-blur-sm\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            src: \"/roukey_logo.png\",\n                                                            alt: \"RouKey\",\n                                                            width: 48,\n                                                            height: 48,\n                                                            className: \"w-12 h-12 object-contain\",\n                                                            priority: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-4xl font-bold mb-4\",\n                                                        children: \"Join RouKey Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-white/90 mb-8\",\n                                                        children: [\n                                                            \"Get started with \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-bold\",\n                                                                children: \"UNLIMITED\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 38\n                                                            }, this),\n                                                            \" access to 300+ AI models\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-white rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white/90\",\n                                                                children: \"No Request Limits\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-white rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white/90\",\n                                                                children: \"300+ AI Models\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-white rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white/90\",\n                                                                children: \"Enterprise Security\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            className: \"w-full max-w-md mx-auto lg:mx-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            className: \"inline-flex items-center space-x-3 mb-8 justify-center lg:justify-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg border border-gray-100 p-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        src: \"/roukey_logo.png\",\n                                                        alt: \"RouKey\",\n                                                        width: 40,\n                                                        height: 40,\n                                                        className: \"w-full h-full object-contain\",\n                                                        priority: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold text-black\",\n                                                    children: \"RouKey\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl font-bold text-black mb-3\",\n                                            children: \"Sign Up\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-lg\",\n                                            children: \"Create your AI gateway account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2 mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        await supabase.auth.signOut();\n                                                        localStorage.clear();\n                                                        window.location.reload();\n                                                    },\n                                                    className: \"text-xs text-gray-400 hover:text-gray-600\",\n                                                    children: \"\\uD83D\\uDD27 Clear Session\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setError('');\n                                                        setFormData({\n                                                            firstName: '',\n                                                            lastName: '',\n                                                            email: '',\n                                                            password: '',\n                                                            confirmPassword: ''\n                                                        });\n                                                    },\n                                                    className: \"text-xs text-gray-400 hover:text-gray-600\",\n                                                    children: \"\\uD83D\\uDDD1️ Clear Form\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedPlan && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 p-4 bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 border border-[#ff6b35]/20 rounded-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-[#ff6b35] font-semibold text-lg\",\n                                                            children: [\n                                                                selectedPlan.charAt(0).toUpperCase() + selectedPlan.slice(1),\n                                                                \" Plan Selected\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-center text-gray-600 text-sm mt-1\",\n                                                    children: \"You'll be redirected to checkout after creating your account\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center mt-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/pricing\",\n                                                        className: \"text-[#ff6b35] hover:text-[#e55a2b] text-sm font-medium transition-colors\",\n                                                        children: \"Change plan\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-3xl shadow-xl border border-gray-100 p-8 backdrop-blur-sm relative overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-5\",\n                                            style: {\n                                                backgroundImage: \"\\n                    linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\\n                    linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\\n                  \",\n                                                backgroundSize: '20px 20px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10\",\n                                            children: [\n                                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-xl\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-600 text-sm\",\n                                                            children: error\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        error.includes('already registered') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/auth/signin\",\n                                                                className: \"text-[#ff6b35] hover:text-[#e55a2b] font-semibold text-sm transition-colors\",\n                                                                children: \"→ Go to Sign In page\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleSubmit,\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"firstName\",\n                                                                            className: \"block text-sm font-semibold text-gray-800 mb-3\",\n                                                                            children: \"\\uD83D\\uDC64 First Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 457,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"firstName\",\n                                                                            name: \"firstName\",\n                                                                            type: \"text\",\n                                                                            required: true,\n                                                                            value: formData.firstName,\n                                                                            onChange: handleChange,\n                                                                            className: \"w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white\",\n                                                                            placeholder: \"John\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 460,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"lastName\",\n                                                                            className: \"block text-sm font-semibold text-gray-800 mb-3\",\n                                                                            children: \"\\uD83D\\uDC64 Last Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 472,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"lastName\",\n                                                                            name: \"lastName\",\n                                                                            type: \"text\",\n                                                                            required: true,\n                                                                            value: formData.lastName,\n                                                                            onChange: handleChange,\n                                                                            className: \"w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white\",\n                                                                            placeholder: \"Doe\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 475,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"email\",\n                                                                    className: \"block text-sm font-semibold text-gray-800 mb-3\",\n                                                                    children: \"\\uD83D\\uDCE7 Email Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"email\",\n                                                                    name: \"email\",\n                                                                    type: \"email\",\n                                                                    autoComplete: \"email\",\n                                                                    required: true,\n                                                                    value: formData.email,\n                                                                    onChange: handleChange,\n                                                                    className: \"w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white\",\n                                                                    placeholder: \"<EMAIL>\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"password\",\n                                                                    className: \"block text-sm font-semibold text-gray-800 mb-3\",\n                                                                    children: \"\\uD83D\\uDD12 Password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"password\",\n                                                                            name: \"password\",\n                                                                            type: showPassword ? 'text' : 'password',\n                                                                            required: true,\n                                                                            value: formData.password,\n                                                                            onChange: handleChange,\n                                                                            className: \"w-full px-5 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white\",\n                                                                            placeholder: \"Create a strong password\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 512,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                                            className: \"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-[#ff6b35] transition-colors\",\n                                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                className: \"h-5 w-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                                lineNumber: 528,\n                                                                                columnNumber: 27\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"h-5 w-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                                lineNumber: 530,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 522,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                formData.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 p-4 bg-gray-50 rounded-xl space-y-2\",\n                                                                    children: passwordRequirements.map((req, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center text-sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                    className: \"h-4 w-4 mr-3 \".concat(req.met ? 'text-green-500' : 'text-gray-300')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                                    lineNumber: 540,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: req.met ? 'text-green-600 font-medium' : 'text-gray-500',\n                                                                                    children: req.text\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                                    lineNumber: 541,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 539,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 537,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"confirmPassword\",\n                                                                    className: \"block text-sm font-semibold text-gray-800 mb-3\",\n                                                                    children: \"\\uD83D\\uDD12 Confirm Password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 550,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"confirmPassword\",\n                                                                            name: \"confirmPassword\",\n                                                                            type: showConfirmPassword ? 'text' : 'password',\n                                                                            required: true,\n                                                                            value: formData.confirmPassword,\n                                                                            onChange: handleChange,\n                                                                            className: \"w-full px-5 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white\",\n                                                                            placeholder: \"Confirm your password\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 554,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                                                            className: \"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-[#ff6b35] transition-colors\",\n                                                                            children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                className: \"h-5 w-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                                lineNumber: 570,\n                                                                                columnNumber: 27\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"h-5 w-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                                lineNumber: 572,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 564,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 553,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start p-4 bg-gray-50 rounded-xl\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"terms\",\n                                                                    name: \"terms\",\n                                                                    type: \"checkbox\",\n                                                                    checked: agreedToTerms,\n                                                                    onChange: (e)=>setAgreedToTerms(e.target.checked),\n                                                                    className: \"h-5 w-5 text-[#ff6b35] focus:ring-[#ff6b35] border-gray-300 rounded-lg mt-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 580,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"terms\",\n                                                                    className: \"ml-3 block text-sm text-gray-700\",\n                                                                    children: [\n                                                                        \"I agree to the\",\n                                                                        ' ',\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                            href: \"/terms\",\n                                                                            className: \"text-[#ff6b35] hover:text-[#e55a2b] font-semibold transition-colors\",\n                                                                            children: \"Terms of Service\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 590,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        ' ',\n                                                                        \"and\",\n                                                                        ' ',\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                            href: \"/privacy\",\n                                                                            className: \"text-[#ff6b35] hover:text-[#e55a2b] font-semibold transition-colors\",\n                                                                            children: \"Privacy Policy\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 594,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 588,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            disabled: isLoading,\n                                                            className: \"w-full bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white py-4 px-6 rounded-xl font-bold text-lg hover:from-[#e55a2b] hover:to-[#e8851a] focus:ring-4 focus:ring-[#ff6b35]/30 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02]\",\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Creating account...\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 606,\n                                                                columnNumber: 23\n                                                            }, this) : 'Create Account'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mt-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-lg\",\n                                        children: [\n                                            \"Already have an account?\",\n                                            ' ',\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/auth/signin\",\n                                                className: \"text-[#ff6b35] hover:text-[#e55a2b] font-bold transition-colors\",\n                                                children: \"Sign in\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n        lineNumber: 263,\n        columnNumber: 5\n    }, this);\n}\n_s(SignUpPageContent, \"BCX0lmGvzjj8LLuQi/+8cID0LMk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams\n    ];\n});\n_c = SignUpPageContent;\nfunction SignUpPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-white flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                        lineNumber: 643,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                        lineNumber: 644,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                lineNumber: 642,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n            lineNumber: 641,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SignUpPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n            lineNumber: 648,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n        lineNumber: 640,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SignUpPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"SignUpPageContent\");\n$RefreshReg$(_c1, \"SignUpPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/signup/page.tsx\n"));

/***/ })

});