import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const redirectTo = requestUrl.searchParams.get('redirectTo');

  console.log('Auth callback:', { code: !!code, redirectTo });

  if (code) {
    const supabase = await createSupabaseServerClientOnRequest();

    try {
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);

      if (error) {
        console.error('Error exchanging code for session:', error);
        return NextResponse.redirect(new URL('/auth/signin?error=auth_callback_error', request.url));
      }

      console.log('Session exchanged successfully for user:', data.user?.id);

      // If this is a redirect to checkout, go there immediately
      if (redirectTo && redirectTo.includes('/checkout')) {
        console.log('Redirecting to checkout:', redirectTo);
        return NextResponse.redirect(new URL(redirectTo, request.url));
      }

      // If this is email verification after payment, check subscription status
      if (redirectTo && redirectTo.includes('/auth/verify-email')) {
        // Check if user has completed payment
        const { data: profile } = await supabase
          .from('user_profiles')
          .select('subscription_status, subscription_tier')
          .eq('user_id', data.user.id)
          .single();

        if (profile && profile.subscription_status === 'active') {
          // Payment completed, redirect to dashboard
          return NextResponse.redirect(new URL('/dashboard', request.url));
        } else {
          // Payment not completed, redirect to verify email page
          return NextResponse.redirect(new URL('/auth/verify-email', request.url));
        }
      }

      // For existing users signing in, check subscription status
      if (data.user) {
        const { data: profile } = await supabase
          .from('user_profiles')
          .select('subscription_status, subscription_tier')
          .eq('user_id', data.user.id)
          .single();

        // If no active subscription, redirect to pricing
        if (!profile || !profile.subscription_status || profile.subscription_status !== 'active') {
          return NextResponse.redirect(new URL('/pricing?checkout=true', request.url));
        }
      }

    } catch (error) {
      console.error('Error exchanging code for session:', error);
      return NextResponse.redirect(new URL('/auth/signin?error=auth_callback_error', request.url));
    }
  }

  // Default redirect
  const finalRedirect = redirectTo || '/dashboard';
  return NextResponse.redirect(new URL(finalRedirect, request.url));
}
