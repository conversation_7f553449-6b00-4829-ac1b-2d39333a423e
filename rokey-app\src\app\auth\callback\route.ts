import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  // IMMEDIATE LOG TO VERIFY ROUTE IS HIT
  console.log('🔥🔥🔥 AUTH CALLBACK ROUTE HIT 🔥🔥🔥');

  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const redirectTo = requestUrl.searchParams.get('redirectTo');

  // SERVER LOG: Auth callback started
  console.log('🔥 AUTH CALLBACK STARTED');
  console.log('🔥 Full URL:', request.url);
  console.log('🔥 Code present:', !!code);
  console.log('🔥 RedirectTo:', redirectTo);

  if (code) {
    const supabase = await createSupabaseServerClientOnRequest();

    try {
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);

      if (error) {
        console.log('🔥 ERROR: Failed to exchange code for session:', error);
        return NextResponse.redirect(new URL('/auth/signin?error=auth_callback_error', request.url));
      }

      console.log('🔥 SUCCESS: Session exchanged for user:', data.user?.id);
      console.log('🔥 User email:', data.user?.email);
      console.log('🔥 User created at:', data.user?.created_at);
      console.log('🔥 User metadata:', data.user?.user_metadata);

      // If this is a redirect to checkout, go there immediately
      if (redirectTo && redirectTo.includes('/checkout')) {
        console.log('🔥 DIRECT CHECKOUT REDIRECT:', redirectTo);
        return NextResponse.redirect(new URL(redirectTo, request.url));
      }

      // If this is after payment, check subscription status and redirect to dashboard
      if (requestUrl.searchParams.get('payment_success') === 'true') {
        // Check if user has completed payment
        const { data: profile } = await supabase
          .from('user_profiles')
          .select('subscription_status, subscription_tier')
          .eq('id', data.user.id)
          .single();

        if (profile && profile.subscription_status === 'active') {
          // Payment completed, redirect to dashboard
          return NextResponse.redirect(new URL('/dashboard', request.url));
        } else {
          // Payment processing, wait a moment and redirect to dashboard anyway
          // The webhook will update the status shortly
          return NextResponse.redirect(new URL('/dashboard', request.url));
        }
      }

      // Handle Google OAuth users exactly like manual signup
      if (data.user) {
        const userMetadata = data.user.user_metadata || {};
        const paymentStatus = userMetadata.payment_status;

        console.log('🔥 CHECKING USER STATUS');
        console.log('🔥 Payment status from metadata:', paymentStatus);

        // Check if this is an existing user with pending payment (like manual signup)
        if (paymentStatus === 'pending') {
          console.log('🔥 FOUND PENDING USER - ALLOWING RETRY');

          // Extract plan from redirect URL
          const urlParams = new URL(redirectTo || '', 'https://example.com');
          const planFromUrl = urlParams.searchParams.get('plan');

          if (planFromUrl && ['starter', 'professional', 'enterprise'].includes(planFromUrl)) {
            const checkoutUrl = `/checkout?plan=${planFromUrl}&user_id=${data.user.id}&email=${encodeURIComponent(data.user.email || '')}`;
            console.log('🔥 PENDING USER REDIRECTING TO CHECKOUT:', checkoutUrl);
            return NextResponse.redirect(new URL(checkoutUrl, request.url));
          }
        }

        // Check subscription status in database for active users
        const { data: profile } = await supabase
          .from('user_profiles')
          .select('subscription_status, subscription_tier')
          .eq('id', data.user.id)
          .single();

        console.log('🔥 Profile from database:', profile);

        // If user has active subscription, continue to dashboard
        if (profile && profile.subscription_status === 'active') {
          console.log('🔥 USER HAS ACTIVE SUBSCRIPTION, CONTINUING TO DASHBOARD');
          const finalRedirect = redirectTo || '/dashboard';
          return NextResponse.redirect(new URL(finalRedirect, request.url));
        }

        // For new Google OAuth users or users without active subscription
        const urlParams = new URL(redirectTo || '', 'https://example.com');
        const planFromUrl = urlParams.searchParams.get('plan');

        console.log('🔥 Plan from URL:', planFromUrl);

        if (planFromUrl && ['starter', 'professional', 'enterprise'].includes(planFromUrl)) {
          // NEW GOOGLE OAUTH USER: Update metadata to mark as pending (like manual signup)
          console.log('🔥 NEW GOOGLE OAUTH USER - SETTING PAYMENT_STATUS TO PENDING');

          // Add debug logging to terminal
          try {
            await fetch(`${request.url.split('/auth/callback')[0]}/api/debug/checkout`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                action: 'GOOGLE_OAUTH_NEW_USER_PENDING',
                userId: data.user.id,
                email: data.user.email,
                planFromUrl,
                userMetadata,
                message: 'Setting Google OAuth user to pending status like manual signup'
              })
            }).catch(() => {});
          } catch (debugErr) {
            console.log('🔥 Debug logging failed:', debugErr);
          }

          try {
            const { error: updateError } = await supabase.auth.updateUser({
              data: {
                ...userMetadata,
                payment_status: 'pending',
                plan: planFromUrl,
                full_name: userMetadata.full_name || userMetadata.name,
              }
            });

            if (updateError) {
              console.log('🔥 ERROR updating user metadata:', updateError);
            } else {
              console.log('🔥 SUCCESS: Updated user metadata with pending status');
            }
          } catch (updateErr) {
            console.log('🔥 ERROR in updateUser:', updateErr);
          }

          const checkoutUrl = `/checkout?plan=${planFromUrl}&user_id=${data.user.id}&email=${encodeURIComponent(data.user.email || '')}`;
          console.log('🔥 NEW OAUTH USER REDIRECTING TO CHECKOUT:', checkoutUrl);
          return NextResponse.redirect(new URL(checkoutUrl, request.url));
        } else {
          // No plan specified - redirect to pricing
          console.log('🔥 NO PLAN FOUND, REDIRECTING TO PRICING');
          return NextResponse.redirect(new URL('/pricing?checkout=true', request.url));
        }
      }

    } catch (error) {
      console.error('Error exchanging code for session:', error);
      return NextResponse.redirect(new URL('/auth/signin?error=auth_callback_error', request.url));
    }
  }

  // Default redirect
  const finalRedirect = redirectTo || '/dashboard';
  console.log('🔥 DEFAULT REDIRECT TO:', finalRedirect);
  return NextResponse.redirect(new URL(finalRedirect, request.url));
}
